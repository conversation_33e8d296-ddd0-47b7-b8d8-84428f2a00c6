package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type sessionCreate struct {
	ClientID string `json:"client_id" validate:"required"`
	WorkerID string `json:"worker_id" validate:"required"`
	TurnID   string `json:"turn_id" validate:"required"`
	Day      int    `json:"day" validate:"required,min=0,max=6"`
	Time     int    `json:"time" validate:"required,min=0,max=23"`
}

func sessionCreateToModel(req sessionCreate) model.SessionCreate {
	return model.SessionCreate{
		ClientID: req.ClientID,
		WorkerID: req.WorkerID,
		TurnID:   req.TurnID,
		Day:      req.Day,
		Time:     req.Time,
	}
}

// Create implements SessionHandler.
func (s *sessionHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[sessionCreate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	id, err := s.useCase.Create(ctx, sessionCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create session")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
