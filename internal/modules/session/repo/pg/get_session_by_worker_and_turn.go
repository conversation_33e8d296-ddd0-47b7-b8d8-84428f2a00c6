package pg

import (
	"context"
	"errors"
	"time"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) GetByWorkerAndTurn(ctx context.Context, workerID string, turnID string) (*model.Session, error) {
	var session model.Session
	var clientID, dbWorkerID *string

	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, client_id, worker_id, turn_id, day, time, created_at, updated_at, deleted_at
            FROM sessions
            WHERE worker_id = $1 AND turn_id = $2 AND deleted_at IS NULL
        `

		row := conn.QueryRow(ctx, query, workerID, turnID)
		err := row.Scan(
			&session.ID,
			&clientID,
			&dbWorkerID,
			&session.TurnID,
			&session.Day,
			&session.Time,
			&session.CreatedAt,
			&session.UpdatedAt,
			&session.DeletedAt,
		)

		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return model.SessionNotFoundf("session not found", err, nil)
			}
			return utils.InternalErrorf("failed to get session", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch the client only if clientID is not null
	if clientID != nil {
		clientQuery := `
			SELECT c.id, c.created_at, c.updated_at, c.deleted_at,
				p.id, p.name, p.father_last_name, p.mother_last_name, p.email, p.address, p.phone,
				p.birth_date, p.gender, p.document, p.document_type, p.created_at, p.updated_at, p.deleted_at
			FROM clients c
			JOIN persons p ON c.person_id = p.id
			WHERE c.id = $1 AND c.deleted_at IS NULL AND p.deleted_at IS NULL
		`

		var client clientModel.Client
		var person personModel.Person
		var birthDate *time.Time
		err = pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
			row := conn.QueryRow(ctx, clientQuery, *clientID)
			return row.Scan(
				&client.ID,
				&client.CreatedAt,
				&client.UpdatedAt,
				&client.DeletedAt,
				&person.ID,
				&person.Name,
				&person.FatherLastName,
				&person.MotherLastName,
				&person.Email,
				&person.Address,
				&person.Phone,
				&birthDate,
				&person.Gender,
				&person.Document,
				&person.DocumentType,
				&person.CreatedAt,
				&person.UpdatedAt,
				&person.DeletedAt,
			)
		})

		if err != nil {
			return nil, utils.InternalErrorf("failed to get client for session", err, nil)
		}

		if birthDate != nil {
			person.BirthDate = &utils.DateOnly{Time: *birthDate}
		}
		client.Person = &person
		session.Client = &client
	}

	// Fetch the worker
	workerQuery := `
		SELECT w.id, w.positions, w.created_at, w.updated_at, w.deleted_at,
			p.id, p.name, p.father_last_name, p.mother_last_name, p.email, p.address, p.phone,
			p.birth_date, p.gender, p.document, p.document_type, p.created_at, p.updated_at, p.deleted_at
		FROM workers w
		JOIN persons p ON w.person_id = p.id
		WHERE w.id = $1 AND w.deleted_at IS NULL AND p.deleted_at IS NULL
	`

	var worker workerModel.Worker
	var workerPerson personModel.Person
	var workerBirthDate *time.Time
	err = pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		row := conn.QueryRow(ctx, workerQuery, *dbWorkerID)
		return row.Scan(
			&worker.ID,
			&worker.Positions,
			&worker.CreatedAt,
			&worker.UpdatedAt,
			&worker.DeletedAt,
			&workerPerson.ID,
			&workerPerson.Name,
			&workerPerson.FatherLastName,
			&workerPerson.MotherLastName,
			&workerPerson.Email,
			&workerPerson.Address,
			&workerPerson.Phone,
			&workerBirthDate,
			&workerPerson.Gender,
			&workerPerson.Document,
			&workerPerson.DocumentType,
			&workerPerson.CreatedAt,
			&workerPerson.UpdatedAt,
			&workerPerson.DeletedAt,
		)
	})

	if err != nil {
		return nil, utils.InternalErrorf("failed to get worker for session", err, nil)
	}

	if workerBirthDate != nil {
		workerPerson.BirthDate = &utils.DateOnly{Time: *workerBirthDate}
	}
	worker.Person = &workerPerson
	session.Worker = &worker

	return &session, nil
}
