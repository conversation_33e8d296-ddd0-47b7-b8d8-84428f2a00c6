package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SessionPostgreRepo interface {
	Create(ctx context.Context, session model.Session) error
	Update(ctx context.Context, session model.Session) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Session, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Session, error)
	Delete(ctx context.Context, id string) error
}

type sessionPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewSessionPostgreRepo(pool *pgxpool.Pool) SessionPostgreRepo {
	return &sessionPostgreRepo{
		pool: pool,
	}
}
