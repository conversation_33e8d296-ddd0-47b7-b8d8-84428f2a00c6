package model

import "context"

type SessionUsecase interface {
	Create(ctx context.Context, session SessionCreate) (string, error)
	Update(ctx context.Context, session SessionUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Session, error)
	GetByClientAndTurn(ctx context.Context, clientID string, turnID string) (*Session, error)
	GetByWorkerAndTurn(ctx context.Context, workerID string, turnID string) (*Session, error)
	GetAll(ctx context.Context) ([]Session, error)
	Delete(ctx context.Context, id string) error
}
